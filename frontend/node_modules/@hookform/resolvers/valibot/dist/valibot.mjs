import{toNestErrors as e}from"@hookform/resolvers";import{appendErrors as r}from"react-hook-form";import{safeParseAsync as s,getDotPath as t}from"valibot";function o(o,i,a){return void 0===a&&(a={}),function(n,u,m){try{var c=!m.shouldUseNativeValidation&&"all"===m.criteriaMode;return Promise.resolve(s(o,n,Object.assign({},i,{abortPipeEarly:!c}))).then(function(s){if(s.issues){for(var o={};s.issues.length;){var i=s.issues[0],u=t(i);if(u&&(o[u]||(o[u]={message:i.message,type:i.type}),c)){var f=o[u].types,l=f&&f[i.type];o[u]=r(u,c,o,i.type,l?[].concat(l,i.message):i.message)}s.issues.shift()}return{values:{},errors:e(o,m)}}return{values:a.raw?Object.assign({},n):s.output,errors:{}}})}catch(e){return Promise.reject(e)}}}export{o as valibotResolver};
//# sourceMappingURL=valibot.module.js.map
