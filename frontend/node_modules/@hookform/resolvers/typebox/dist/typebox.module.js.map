{"version": 3, "file": "typebox.module.js", "sources": ["../src/typebox.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { Static, StaticDecode, TObject } from '@sinclair/typebox';\nimport { TypeCheck } from '@sinclair/typebox/compiler';\nimport { Value, type ValueError } from '@sinclair/typebox/value';\nimport { FieldError, Resolver, appendErrors } from 'react-hook-form';\n\nfunction parseErrorSchema(\n  _errors: ValueError[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n  for (; _errors.length; ) {\n    const error = _errors[0];\n    const { type, message, path } = error;\n    const _path = path.substring(1).replace(/\\//g, '.');\n\n    if (!errors[_path]) {\n      errors[_path] = { message, type: '' + type };\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types['' + type];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        '' + type,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    _errors.shift();\n  }\n\n  return errors;\n}\n\n/**\n * Creates a resolver for react-hook-form using Typebox schema validation\n * @param {Schema | TypeCheck<Schema>} schema - The Typebox schema to validate against\n * @param {Object} options - Additional resolver configuration\n * @param {string} [options.mode='async'] - Validation mode\n * @returns {Resolver<Static<Schema>>} A resolver function compatible with react-hook-form\n * @example\n * const schema = Type.Object({\n *   name: Type.String(),\n *   age: Type.Number()\n * });\n *\n * useForm({\n *   resolver: typeboxResolver(schema)\n * });\n */\nexport function typeboxResolver<Schema extends TObject, Context>(\n  schema: Schema | TypeCheck<Schema>,\n): Resolver<Static<Schema>, Context, StaticDecode<Schema>> {\n  return async (values: Static<Schema>, _, options) => {\n    const errors = Array.from(\n      schema instanceof TypeCheck\n        ? schema.Errors(values)\n        : Value.Errors(schema, values),\n    );\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    if (!errors.length) {\n      return {\n        errors: {},\n        values,\n      };\n    }\n\n    return {\n      values: {},\n      errors: toNestErrors(\n        parseErrorSchema(\n          errors,\n          !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n        ),\n        options,\n      ),\n    };\n  };\n}\n"], "names": ["parseErrorSchema", "_errors", "validateAllFieldCriteria", "errors", "length", "error", "type", "message", "_path", "path", "substring", "replace", "types", "messages", "appendErrors", "concat", "shift", "typeboxResolver", "schema", "values", "_", "options", "Array", "from", "TypeCheck", "Errors", "Value", "shouldUseNativeValidation", "validateFieldsNatively", "Promise", "resolve", "toNestErrors", "criteriaMode", "e", "reject"], "mappings": "qOAMA,SAASA,EACPC,EACAC,GAGA,IADA,IAAMC,EAAqC,CAAE,EACtCF,EAAQG,QAAU,CACvB,IAAMC,EAAQJ,EAAQ,GACdK,EAAwBD,EAAxBC,KAAMC,EAAkBF,EAAlBE,QACRC,EAD0BH,EAATI,KACJC,UAAU,GAAGC,QAAQ,MAAO,KAM/C,GAJKR,EAAOK,KACVL,EAAOK,GAAS,CAAED,QAAAA,EAASD,KAAM,GAAKA,IAGpCJ,EAA0B,CAC5B,IAAMU,EAAQT,EAAOK,GAAOI,MACtBC,EAAWD,GAASA,EAAM,GAAKN,GAErCH,EAAOK,GAASM,EACdN,EACAN,EACAC,EACA,GAAKG,EACLO,EACK,GAAgBE,OAAOF,EAAsBR,EAAME,SACpDF,EAAME,QAEd,CAEAN,EAAQe,OACV,CAEA,OAAOb,CACT,CAkBgB,SAAAc,EACdC,GAEA,OAAcC,SAAAA,EAAwBC,EAAGC,GAAO,IAC9C,IAAMlB,EAASmB,MAAMC,KACnBL,aAAkBM,EACdN,EAAOO,OAAON,GACdO,EAAMD,OAAOP,EAAQC,IAK3B,OAFAE,EAAQM,2BAA6BC,EAAuB,CAAE,EAAEP,GAShEQ,QAAAC,QAPK3B,EAAOC,OAOL,CACLe,OAAQ,CAAE,EACVhB,OAAQ4B,EACN/B,EACEG,GACCkB,EAAQM,2BAAsD,QAAzBN,EAAQW,cAEhDX,IAbK,CACLlB,OAAQ,GACRgB,OAAAA,GAcN,CAAC,MAAAc,UAAAJ,QAAAK,OAAAD,EACH,CAAA,CAAA"}