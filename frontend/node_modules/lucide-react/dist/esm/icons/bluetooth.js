/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [["path", { d: "m7 7 10 10-5 5V2l5 5L7 17", key: "1q5490" }]];
const Bluetooth = createLucideIcon("bluetooth", __iconNode);

export { __iconNode, Bluetooth as default };
//# sourceMappingURL=bluetooth.js.map
