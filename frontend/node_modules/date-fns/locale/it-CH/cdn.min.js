(()=>{var I;function z(G,J){var X=Object.keys(G);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(G);J&&(Z=Z.filter(function(U){return Object.getOwnPropertyDescriptor(G,U).enumerable})),X.push.apply(X,Z)}return X}function A(G){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?z(Object(X),!0).forEach(function(Z){P(G,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(X)):z(Object(X)).forEach(function(Z){Object.defineProperty(G,Z,Object.getOwnPropertyDescriptor(X,Z))})}return G}function P(G,J,X){if(J=D(J),J in G)Object.defineProperty(G,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else G[J]=X;return G}function D(G){var J=F(G,"string");return K(J)=="symbol"?J:String(J)}function F(G,J){if(K(G)!="object"||!G)return G;var X=G[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(G,J||"default");if(K(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(G)}function w(G,J){return f(G)||b(G,J)||h(G,J)||v()}function v(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(G,J){if(!G)return;if(typeof G==="string")return R(G,J);var X=Object.prototype.toString.call(G).slice(8,-1);if(X==="Object"&&G.constructor)X=G.constructor.name;if(X==="Map"||X==="Set")return Array.from(G);if(X==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(X))return R(G,J)}function R(G,J){if(J==null||J>G.length)J=G.length;for(var X=0,Z=new Array(J);X<J;X++)Z[X]=G[X];return Z}function b(G,J){var X=G==null?null:typeof Symbol!="undefined"&&G[Symbol.iterator]||G["@@iterator"];if(X!=null){var Z,U,B,C,Q=[],q=!0,Y=!1;try{if(B=(X=X.call(G)).next,J===0){if(Object(X)!==X)return;q=!1}else for(;!(q=(Z=B.call(X)).done)&&(Q.push(Z.value),Q.length!==J);q=!0);}catch(H){Y=!0,U=H}finally{try{if(!q&&X.return!=null&&(C=X.return(),Object(C)!==C))return}finally{if(Y)throw U}}return Q}}function f(G){if(Array.isArray(G))return G}function K(G){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},K(G)}var k=Object.defineProperty,OG=function G(J,X){for(var Z in X)k(J,Z,{get:X[Z],enumerable:!0,configurable:!0,set:function U(B){return X[Z]=function(){return B}}})},m={lessThanXSeconds:{one:"meno di un secondo",other:"meno di {{count}} secondi"},xSeconds:{one:"un secondo",other:"{{count}} secondi"},halfAMinute:"alcuni secondi",lessThanXMinutes:{one:"meno di un minuto",other:"meno di {{count}} minuti"},xMinutes:{one:"un minuto",other:"{{count}} minuti"},aboutXHours:{one:"circa un'ora",other:"circa {{count}} ore"},xHours:{one:"un'ora",other:"{{count}} ore"},xDays:{one:"un giorno",other:"{{count}} giorni"},aboutXWeeks:{one:"circa una settimana",other:"circa {{count}} settimane"},xWeeks:{one:"una settimana",other:"{{count}} settimane"},aboutXMonths:{one:"circa un mese",other:"circa {{count}} mesi"},xMonths:{one:"un mese",other:"{{count}} mesi"},aboutXYears:{one:"circa un anno",other:"circa {{count}} anni"},xYears:{one:"un anno",other:"{{count}} anni"},overXYears:{one:"pi\xF9 di un anno",other:"pi\xF9 di {{count}} anni"},almostXYears:{one:"quasi un anno",other:"quasi {{count}} anni"}},_=function G(J,X,Z){var U,B=m[J];if(typeof B==="string")U=B;else if(X===1)U=B.one;else U=B.other.replace("{{count}}",X.toString());if(Z!==null&&Z!==void 0&&Z.addSuffix)if(Z.comparison&&Z.comparison>0)return"tra "+U;else return U+" fa";return U},PG=7,g=365.2425,c=Math.pow(10,8)*24*60*60*1000,DG=-c,FG=604800000,wG=86400000,vG=60000,hG=3600000,bG=1000,fG=525600,kG=43200,mG=1440,_G=60,gG=3,cG=12,yG=4,y=3600,uG=60,V=y*24,pG=V*7,u=V*g,p=u/12,dG=p*3,W=Symbol.for("constructDateFrom");function S(G,J){if(typeof G==="function")return G(J);if(G&&K(G)==="object"&&W in G)return G[W](J);if(G instanceof Date)return new G.constructor(J);return new Date(J)}function d(G){for(var J=arguments.length,X=new Array(J>1?J-1:0),Z=1;Z<J;Z++)X[Z-1]=arguments[Z];var U=S.bind(null,G||X.find(function(B){return K(B)==="object"}));return X.map(U)}function l(){return $}function lG(G){$=G}var $={};function i(G,J){return S(J||G,G)}function j(G,J){var X,Z,U,B,C,Q,q=l(),Y=(X=(Z=(U=(B=J===null||J===void 0?void 0:J.weekStartsOn)!==null&&B!==void 0?B:J===null||J===void 0||(C=J.locale)===null||C===void 0||(C=C.options)===null||C===void 0?void 0:C.weekStartsOn)!==null&&U!==void 0?U:q.weekStartsOn)!==null&&Z!==void 0?Z:(Q=q.locale)===null||Q===void 0||(Q=Q.options)===null||Q===void 0?void 0:Q.weekStartsOn)!==null&&X!==void 0?X:0,H=i(G,J===null||J===void 0?void 0:J.in),N=H.getDay(),LG=(N<Y?7:0)+N-Y;return H.setDate(H.getDate()-LG),H.setHours(0,0,0,0),H}function L(G,J,X){var Z=d(X===null||X===void 0?void 0:X.in,G,J),U=w(Z,2),B=U[0],C=U[1];return+j(B,X)===+j(C,X)}function n(G){switch(G){case 0:return"'domenica scorsa alle' p";default:return"'"+M[G]+" scorso alle' p"}}function O(G){return"'"+M[G]+" alle' p"}function s(G){switch(G){case 0:return"'domenica prossima alle' p";default:return"'"+M[G]+" prossimo alle' p"}}var M=["domenica","luned\xEC","marted\xEC","mercoled\xEC","gioved\xEC","venerd\xEC","sabato"],r={lastWeek:function G(J,X,Z){var U=J.getDay();if(L(J,X,Z))return O(U);else return n(U)},yesterday:"'ieri alle' p",today:"'oggi alle' p",tomorrow:"'domani alle' p",nextWeek:function G(J,X,Z){var U=J.getDay();if(L(J,X,Z))return O(U);else return s(U)},other:"P"},o=function G(J,X,Z,U){var B=r[J];if(typeof B==="function")return B(X,Z,U);return B};function E(G){return function(J,X){var Z=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",U;if(Z==="formatting"&&G.formattingValues){var B=G.defaultFormattingWidth||G.defaultWidth,C=X!==null&&X!==void 0&&X.width?String(X.width):B;U=G.formattingValues[C]||G.formattingValues[B]}else{var Q=G.defaultWidth,q=X!==null&&X!==void 0&&X.width?String(X.width):G.defaultWidth;U=G.values[q]||G.values[Q]}var Y=G.argumentCallback?G.argumentCallback(J):J;return U[Y]}}var a={narrow:["aC","dC"],abbreviated:["a.C.","d.C."],wide:["avanti Cristo","dopo Cristo"]},e={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xBA trimestre","2\xBA trimestre","3\xBA trimestre","4\xBA trimestre"]},t={narrow:["G","F","M","A","M","G","L","A","S","O","N","D"],abbreviated:["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic"],wide:["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre"]},GG={narrow:["D","L","M","M","G","V","S"],short:["dom","lun","mar","mer","gio","ven","sab"],abbreviated:["dom","lun","mar","mer","gio","ven","sab"],wide:["domenica","luned\xEC","marted\xEC","mercoled\xEC","gioved\xEC","venerd\xEC","sabato"]},JG={narrow:{am:"m.",pm:"p.",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"},abbreviated:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"},wide:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"}},XG={narrow:{am:"m.",pm:"p.",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"},abbreviated:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"},wide:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"}},ZG=function G(J,X){var Z=Number(J);return String(Z)},UG={ordinalNumber:ZG,era:E({values:a,defaultWidth:"wide"}),quarter:E({values:e,defaultWidth:"wide",argumentCallback:function G(J){return J-1}}),month:E({values:t,defaultWidth:"wide"}),day:E({values:GG,defaultWidth:"wide"}),dayPeriod:E({values:JG,defaultWidth:"wide",formattingValues:XG,defaultFormattingWidth:"wide"})};function T(G){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=X.width,U=Z&&G.matchPatterns[Z]||G.matchPatterns[G.defaultMatchWidth],B=J.match(U);if(!B)return null;var C=B[0],Q=Z&&G.parsePatterns[Z]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(Q)?CG(Q,function(N){return N.test(C)}):BG(Q,function(N){return N.test(C)}),Y;Y=G.valueCallback?G.valueCallback(q):q,Y=X.valueCallback?X.valueCallback(Y):Y;var H=J.slice(C.length);return{value:Y,rest:H}}}function BG(G,J){for(var X in G)if(Object.prototype.hasOwnProperty.call(G,X)&&J(G[X]))return X;return}function CG(G,J){for(var X=0;X<G.length;X++)if(J(G[X]))return X;return}function QG(G){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=J.match(G.matchPattern);if(!Z)return null;var U=Z[0],B=J.match(G.parsePattern);if(!B)return null;var C=G.valueCallback?G.valueCallback(B[0]):B[0];C=X.valueCallback?X.valueCallback(C):C;var Q=J.slice(U.length);return{value:C,rest:Q}}}var YG=/^(\d+)(º)?/i,qG=/\d+/i,HG={narrow:/^(aC|dC)/i,abbreviated:/^(a\.?\s?C\.?|a\.?\s?e\.?\s?v\.?|d\.?\s?C\.?|e\.?\s?v\.?)/i,wide:/^(avanti Cristo|avanti Era Volgare|dopo Cristo|Era Volgare)/i},KG={any:[/^a/i,/^(d|e)/i]},NG={narrow:/^[1234]/i,abbreviated:/^t[1234]/i,wide:/^[1234](º)? trimestre/i},EG={any:[/1/i,/2/i,/3/i,/4/i]},TG={narrow:/^[gfmalsond]/i,abbreviated:/^(gen|feb|mar|apr|mag|giu|lug|ago|set|ott|nov|dic)/i,wide:/^(gennaio|febbraio|marzo|aprile|maggio|giugno|luglio|agosto|settembre|ottobre|novembre|dicembre)/i},AG={narrow:[/^g/i,/^f/i,/^m/i,/^a/i,/^m/i,/^g/i,/^l/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ge/i,/^f/i,/^mar/i,/^ap/i,/^mag/i,/^gi/i,/^l/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},IG={narrow:/^[dlmgvs]/i,short:/^(do|lu|ma|me|gi|ve|sa)/i,abbreviated:/^(dom|lun|mar|mer|gio|ven|sab)/i,wide:/^(domenica|luned[i|ì]|marted[i|ì]|mercoled[i|ì]|gioved[i|ì]|venerd[i|ì]|sabato)/i},MG={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^g/i,/^v/i,/^s/i],any:[/^d/i,/^l/i,/^ma/i,/^me/i,/^g/i,/^v/i,/^s/i]},xG={narrow:/^(a|m\.|p|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i,any:/^([ap]\.?\s?m\.?|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i},zG={any:{am:/^a/i,pm:/^p/i,midnight:/^mezza/i,noon:/^mezzo/i,morning:/mattina/i,afternoon:/pomeriggio/i,evening:/sera/i,night:/notte/i}},RG={ordinalNumber:QG({matchPattern:YG,parsePattern:qG,valueCallback:function G(J){return parseInt(J,10)}}),era:T({matchPatterns:HG,defaultMatchWidth:"wide",parsePatterns:KG,defaultParseWidth:"any"}),quarter:T({matchPatterns:NG,defaultMatchWidth:"wide",parsePatterns:EG,defaultParseWidth:"any",valueCallback:function G(J){return J+1}}),month:T({matchPatterns:TG,defaultMatchWidth:"wide",parsePatterns:AG,defaultParseWidth:"any"}),day:T({matchPatterns:IG,defaultMatchWidth:"wide",parsePatterns:MG,defaultParseWidth:"any"}),dayPeriod:T({matchPatterns:xG,defaultMatchWidth:"any",parsePatterns:zG,defaultParseWidth:"any"})};function x(G){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=J.width?String(J.width):G.defaultWidth,Z=G.formats[X]||G.formats[G.defaultWidth];return Z}}var VG={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd.MM.y"},WG={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},SG={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},$G={date:x({formats:VG,defaultWidth:"full"}),time:x({formats:WG,defaultWidth:"full"}),dateTime:x({formats:SG,defaultWidth:"full"})},jG={code:"it-CH",formatDistance:_,formatLong:$G,formatRelative:o,localize:UG,match:RG,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=A(A({},window.dateFns),{},{locale:A(A({},(I=window.dateFns)===null||I===void 0?void 0:I.locale),{},{itCH:jG})})})();

//# debugId=AE6B8442B1E79B9F64756E2164756E21
